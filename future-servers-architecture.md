# Future Servers Architecture Plan

## Overview
This document outlines the extensible architecture design for future Tests and IELTS servers, ensuring seamless integration with the existing Staff and Students servers while maintaining scalability and security.

## Extensible Architecture Principles

### 1. Microservices Design Patterns
```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           Service Mesh Architecture                          │
├─────────────────┬─────────────────┬─────────────────┬─────────────────────────┤
│   Staff Server  │ Students Server │  Tests Server   │    IELTS Server         │
│   (Existing)    │   (Existing)    │   (Future)      │    (Future)             │
├─────────────────┼─────────────────┼─────────────────┼─────────────────────────┤
│ • User Mgmt     │ • Student Portal│ • Test Engine   │ • IELTS Tests           │
│ • Financial     │ • Progress      │ • Question Bank │ • Score Reports         │
│ • Reports       │ • Payments      │ • Analytics     │ • Certificates          │
│ • Groups        │ • Attendance    │ • Scheduling    │ • Prep Materials        │
│ • Leads         │ • Messages      │ • Results       │ • Mock Tests            │
└─────────────────┴─────────────────┴─────────────────┴─────────────────────────┘
                                    │
                    ┌───────────────────────────────────┐
                    │        Shared Services            │
                    ├───────────────────────────────────┤
                    │ • Authentication Service          │
                    │ • Notification Service            │
                    │ • File Storage Service            │
                    │ • Analytics Service               │
                    │ • Audit Logging Service           │
                    │ • Configuration Service           │
                    └───────────────────────────────────┘
```

### 2. Database Architecture Evolution
```
Current State:
┌─────────────────┐    ┌─────────────────┐
│  Staff Database │    │Students Database│
│   (crm-staff)   │    │ (crm-students)  │
└─────────────────┘    └─────────────────┘

Future State:
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Staff Database │    │Students Database│    │ Tests Database  │    │ IELTS Database  │
│   (crm-staff)   │    │ (crm-students)  │    │  (crm-tests)    │    │  (crm-ielts)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │                       │
         └───────────────────────┼───────────────────────┼───────────────────────┘
                                 │                       │
                    ┌─────────────────────────────────────┐
                    │      Shared Reference Data          │
                    │    (Cross-server synchronization)   │
                    └─────────────────────────────────────┘
```

## Tests Server Architecture

### Core Features & Responsibilities
```typescript
interface TestsServerFeatures {
  testManagement: {
    createTest: () => void;
    editTest: () => void;
    deleteTest: () => void;
    duplicateTest: () => void;
    scheduleTest: () => void;
  };
  
  questionBank: {
    createQuestion: () => void;
    categorizeQuestions: () => void;
    importQuestions: () => void;
    exportQuestions: () => void;
    validateQuestions: () => void;
  };
  
  testExecution: {
    startTest: () => void;
    submitAnswers: () => void;
    autoSave: () => void;
    timeManagement: () => void;
    proctoring: () => void;
  };
  
  grading: {
    autoGrade: () => void;
    manualGrade: () => void;
    rubricGrading: () => void;
    peerReview: () => void;
    gradingAnalytics: () => void;
  };
  
  analytics: {
    testPerformance: () => void;
    questionAnalysis: () => void;
    studentProgress: () => void;
    difficultyAnalysis: () => void;
    cheatingDetection: () => void;
  };
}
```

### Database Schema Design
```sql
-- Tests Database (crm-tests)
CREATE TABLE test_templates (
  id VARCHAR PRIMARY KEY,
  name VARCHAR NOT NULL,
  description TEXT,
  subject VARCHAR NOT NULL,
  level course_level NOT NULL,
  duration INTEGER NOT NULL, -- in minutes
  total_points INTEGER NOT NULL,
  passing_score INTEGER NOT NULL,
  instructions TEXT,
  settings JSONB NOT NULL, -- Test configuration
  created_by VARCHAR NOT NULL, -- Reference to staff user
  branch VARCHAR NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE questions (
  id VARCHAR PRIMARY KEY,
  question_text TEXT NOT NULL,
  question_type question_type NOT NULL, -- 'MULTIPLE_CHOICE', 'TRUE_FALSE', 'ESSAY', 'FILL_BLANK'
  options JSONB, -- For multiple choice questions
  correct_answer TEXT,
  points INTEGER NOT NULL,
  difficulty difficulty_level NOT NULL, -- 'EASY', 'MEDIUM', 'HARD'
  subject VARCHAR NOT NULL,
  level course_level NOT NULL,
  tags VARCHAR[],
  explanation TEXT,
  created_by VARCHAR NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE test_questions (
  id VARCHAR PRIMARY KEY,
  test_template_id VARCHAR NOT NULL REFERENCES test_templates(id),
  question_id VARCHAR NOT NULL REFERENCES questions(id),
  order_number INTEGER NOT NULL,
  points_override INTEGER, -- Override question's default points
  created_at TIMESTAMP DEFAULT NOW(),
  
  UNIQUE(test_template_id, question_id),
  UNIQUE(test_template_id, order_number)
);

CREATE TABLE test_instances (
  id VARCHAR PRIMARY KEY,
  test_template_id VARCHAR NOT NULL REFERENCES test_templates(id),
  student_reference_id VARCHAR NOT NULL, -- Reference to student in students DB
  group_reference_id VARCHAR, -- Reference to group in staff DB
  scheduled_start TIMESTAMP NOT NULL,
  scheduled_end TIMESTAMP NOT NULL,
  actual_start TIMESTAMP,
  actual_end TIMESTAMP,
  status test_status DEFAULT 'SCHEDULED', -- 'SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'EXPIRED', 'CANCELLED'
  score INTEGER,
  percentage DECIMAL,
  passed BOOLEAN,
  answers JSONB, -- Student's answers
  time_spent INTEGER, -- in seconds
  proctoring_data JSONB, -- Proctoring information
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE test_results (
  id VARCHAR PRIMARY KEY,
  test_instance_id VARCHAR NOT NULL REFERENCES test_instances(id),
  question_id VARCHAR NOT NULL REFERENCES questions(id),
  student_answer TEXT,
  is_correct BOOLEAN,
  points_earned INTEGER,
  time_spent INTEGER, -- in seconds
  flagged_for_review BOOLEAN DEFAULT false,
  graded_by VARCHAR, -- Reference to staff user who graded
  graded_at TIMESTAMP,
  feedback TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Reference tables for cross-server data
CREATE TABLE student_references (
  id VARCHAR PRIMARY KEY,
  name VARCHAR NOT NULL,
  phone VARCHAR NOT NULL,
  level course_level NOT NULL,
  branch VARCHAR NOT NULL,
  status student_status NOT NULL,
  last_synced_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE group_references (
  id VARCHAR PRIMARY KEY,
  name VARCHAR NOT NULL,
  teacher_name VARCHAR NOT NULL,
  course_name VARCHAR NOT NULL,
  branch VARCHAR NOT NULL,
  last_synced_at TIMESTAMP DEFAULT NOW()
);
```

### API Endpoints Structure
```typescript
// Tests Server API Routes
interface TestsServerAPI {
  // Test Management
  'POST /api/tests': CreateTestTemplate;
  'GET /api/tests': GetTestTemplates;
  'GET /api/tests/:id': GetTestTemplate;
  'PUT /api/tests/:id': UpdateTestTemplate;
  'DELETE /api/tests/:id': DeleteTestTemplate;
  
  // Question Bank
  'POST /api/questions': CreateQuestion;
  'GET /api/questions': GetQuestions;
  'PUT /api/questions/:id': UpdateQuestion;
  'DELETE /api/questions/:id': DeleteQuestion;
  'POST /api/questions/import': ImportQuestions;
  'GET /api/questions/export': ExportQuestions;
  
  // Test Execution
  'POST /api/test-instances': ScheduleTest;
  'GET /api/test-instances/:id': GetTestInstance;
  'POST /api/test-instances/:id/start': StartTest;
  'POST /api/test-instances/:id/submit': SubmitTest;
  'POST /api/test-instances/:id/save': SaveProgress;
  
  // Grading
  'POST /api/test-instances/:id/grade': GradeTest;
  'GET /api/test-results/:instanceId': GetTestResults;
  'PUT /api/test-results/:id': UpdateGrade;
  
  // Analytics
  'GET /api/analytics/test-performance': GetTestPerformance;
  'GET /api/analytics/question-analysis': GetQuestionAnalysis;
  'GET /api/analytics/student-progress': GetStudentProgress;
  
  // Inter-server communication
  'POST /api/inter-server/students/sync': SyncStudentData;
  'POST /api/inter-server/groups/sync': SyncGroupData;
  'GET /api/inter-server/test-results/:studentId': GetStudentTestResults;
}
```

## IELTS Server Architecture

### Specialized IELTS Features
```typescript
interface IELTSServerFeatures {
  testTypes: {
    academicIELTS: () => void;
    generalTrainingIELTS: () => void;
    IELTSIndicator: () => void;
    mockTests: () => void;
  };
  
  skillAssessment: {
    listening: {
      audioPlayback: () => void;
      multipleChoice: () => void;
      fillInBlanks: () => void;
      matching: () => void;
    };
    reading: {
      passageDisplay: () => void;
      comprehensionQuestions: () => void;
      timeManagement: () => void;
    };
    writing: {
      task1: () => void; // Academic: graphs, General: letters
      task2: () => void; // Essays
      wordCount: () => void;
      plagiarismCheck: () => void;
    };
    speaking: {
      recordingInterface: () => void;
      part1Interview: () => void;
      part2CueCard: () => void;
      part3Discussion: () => void;
      aiAssessment: () => void;
    };
  };
  
  bandScoring: {
    calculateBandScore: () => void;
    skillBreakdown: () => void;
    overallBand: () => void;
    scorePrediction: () => void;
  };
  
  preparation: {
    studyMaterials: () => void;
    practiceSessions: () => void;
    vocabularyBuilder: () => void;
    grammarExercises: () => void;
    tipsTechniques: () => void;
  };
  
  certification: {
    generateCertificate: () => void;
    verifyResults: () => void;
    sendResults: () => void;
    resultHistory: () => void;
  };
}
```

### IELTS Database Schema
```sql
-- IELTS Database (crm-ielts)
CREATE TABLE ielts_tests (
  id VARCHAR PRIMARY KEY,
  test_type ielts_test_type NOT NULL, -- 'ACADEMIC', 'GENERAL_TRAINING', 'INDICATOR'
  test_date DATE NOT NULL,
  registration_deadline DATE NOT NULL,
  max_candidates INTEGER NOT NULL,
  fee DECIMAL NOT NULL,
  venue VARCHAR NOT NULL,
  status ielts_test_status DEFAULT 'OPEN', -- 'OPEN', 'FULL', 'CLOSED', 'COMPLETED'
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE ielts_registrations (
  id VARCHAR PRIMARY KEY,
  test_id VARCHAR NOT NULL REFERENCES ielts_tests(id),
  student_reference_id VARCHAR NOT NULL,
  registration_date TIMESTAMP DEFAULT NOW(),
  payment_status payment_status DEFAULT 'PENDING',
  payment_reference VARCHAR,
  special_requirements TEXT,
  status registration_status DEFAULT 'REGISTERED', -- 'REGISTERED', 'CONFIRMED', 'CANCELLED'
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE ielts_test_sessions (
  id VARCHAR PRIMARY KEY,
  test_id VARCHAR NOT NULL REFERENCES ielts_tests(id),
  student_reference_id VARCHAR NOT NULL,
  listening_score DECIMAL,
  reading_score DECIMAL,
  writing_score DECIMAL,
  speaking_score DECIMAL,
  overall_band DECIMAL,
  started_at TIMESTAMP,
  completed_at TIMESTAMP,
  status session_status DEFAULT 'SCHEDULED', -- 'SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'
  answers JSONB, -- All test answers
  recordings JSONB, -- Speaking test recordings
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE ielts_speaking_sessions (
  id VARCHAR PRIMARY KEY,
  test_session_id VARCHAR NOT NULL REFERENCES ielts_test_sessions(id),
  examiner_reference_id VARCHAR, -- Reference to examiner in staff DB
  part1_recording_url VARCHAR,
  part2_recording_url VARCHAR,
  part3_recording_url VARCHAR,
  part1_score DECIMAL,
  part2_score DECIMAL,
  part3_score DECIMAL,
  fluency_score DECIMAL,
  lexical_resource_score DECIMAL,
  grammatical_range_score DECIMAL,
  pronunciation_score DECIMAL,
  examiner_notes TEXT,
  ai_assessment JSONB, -- AI-powered assessment data
  scheduled_at TIMESTAMP,
  started_at TIMESTAMP,
  completed_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE ielts_writing_tasks (
  id VARCHAR PRIMARY KEY,
  test_session_id VARCHAR NOT NULL REFERENCES ielts_test_sessions(id),
  task_number INTEGER NOT NULL, -- 1 or 2
  task_type VARCHAR NOT NULL, -- 'GRAPH', 'LETTER', 'ESSAY'
  prompt TEXT NOT NULL,
  student_response TEXT,
  word_count INTEGER,
  task_achievement_score DECIMAL,
  coherence_cohesion_score DECIMAL,
  lexical_resource_score DECIMAL,
  grammatical_range_score DECIMAL,
  examiner_reference_id VARCHAR,
  examiner_feedback TEXT,
  plagiarism_score DECIMAL,
  ai_assessment JSONB,
  submitted_at TIMESTAMP,
  graded_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE ielts_certificates (
  id VARCHAR PRIMARY KEY,
  test_session_id VARCHAR NOT NULL REFERENCES ielts_test_sessions(id),
  certificate_number VARCHAR UNIQUE NOT NULL,
  issue_date DATE NOT NULL,
  expiry_date DATE NOT NULL,
  verification_code VARCHAR UNIQUE NOT NULL,
  pdf_url VARCHAR,
  sent_to_student BOOLEAN DEFAULT false,
  sent_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);
```

## Shared Services Architecture

### 1. Authentication Service
```typescript
// shared/services/auth-service.ts
export class AuthenticationService {
  async validateToken(token: string): Promise<UserSession | null> {
    // Validate JWT token across all servers
  }
  
  async refreshToken(refreshToken: string): Promise<TokenPair | null> {
    // Refresh authentication tokens
  }
  
  async revokeToken(token: string): Promise<void> {
    // Revoke token across all servers
  }
  
  async getUserPermissions(userId: string, serverType: string): Promise<Permission[]> {
    // Get user permissions for specific server
  }
}
```

### 2. Notification Service
```typescript
// shared/services/notification-service.ts
export class NotificationService {
  async sendEmail(notification: EmailNotification): Promise<void> {
    // Send email notifications
  }
  
  async sendSMS(notification: SMSNotification): Promise<void> {
    // Send SMS notifications
  }
  
  async sendPushNotification(notification: PushNotification): Promise<void> {
    // Send push notifications
  }
  
  async broadcastToServers(event: ServerEvent): Promise<void> {
    // Broadcast events to all servers
  }
}
```

### 3. File Storage Service
```typescript
// shared/services/file-storage-service.ts
export class FileStorageService {
  async uploadFile(file: File, metadata: FileMetadata): Promise<FileReference> {
    // Upload files to shared storage
  }
  
  async downloadFile(fileId: string): Promise<File> {
    // Download files from shared storage
  }
  
  async deleteFile(fileId: string): Promise<void> {
    // Delete files from shared storage
  }
  
  async generateSignedUrl(fileId: string, expiresIn: number): Promise<string> {
    // Generate signed URLs for secure file access
  }
}
```

## Integration Strategy

### 1. Gradual Rollout Plan
```
Phase 1: Tests Server (Months 1-3)
├── Basic test creation and management
├── Question bank functionality
├── Simple test execution
└── Integration with Staff and Students servers

Phase 2: Enhanced Testing (Months 4-6)
├── Advanced analytics
├── Proctoring features
├── Automated grading
└── Performance optimization

Phase 3: IELTS Server (Months 7-9)
├── IELTS-specific test engine
├── Speaking test recording
├── Band score calculation
└── Certificate generation

Phase 4: Advanced Features (Months 10-12)
├── AI-powered assessment
├── Adaptive testing
├── Advanced analytics
└── Mobile applications
```

### 2. Data Migration Strategy
```typescript
// Migration strategy for adding new servers
interface MigrationPlan {
  phase1: {
    extractTestData: () => void;
    createTestsDatabase: () => void;
    migrateBasicTests: () => void;
    setupReferences: () => void;
  };
  
  phase2: {
    extractIELTSData: () => void;
    createIELTSDatabase: () => void;
    migrateIELTSTests: () => void;
    setupCertification: () => void;
  };
  
  validation: {
    verifyDataIntegrity: () => void;
    testCrossServerCommunication: () => void;
    validatePermissions: () => void;
    performanceTest: () => void;
  };
}
```

### 3. API Gateway Implementation
```typescript
// API Gateway for routing requests to appropriate servers
export class APIGateway {
  private routes = {
    '/api/staff/**': 'STAFF_SERVER',
    '/api/students/**': 'STUDENTS_SERVER',
    '/api/tests/**': 'TESTS_SERVER',
    '/api/ielts/**': 'IELTS_SERVER',
  };
  
  async routeRequest(request: Request): Promise<Response> {
    const serverType = this.determineServer(request.url);
    const targetServer = this.getServerInstance(serverType);
    
    return await this.forwardRequest(request, targetServer);
  }
  
  private determineServer(url: string): ServerType {
    // Route determination logic
  }
  
  private async forwardRequest(request: Request, server: ServerInstance): Promise<Response> {
    // Request forwarding with load balancing
  }
}
```

This extensible architecture ensures that future Tests and IELTS servers can be seamlessly integrated into the existing multi-server CRM system while maintaining performance, security, and scalability.
